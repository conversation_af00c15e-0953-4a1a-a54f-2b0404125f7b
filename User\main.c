/**
 ******************************************************************************
 * @file    Project/STM32F4xx_StdPeriph_Templates/main.c
 * <AUTHOR> Application Team
 * @version V1.8.1
 * @date    27-January-2022
 * @brief   Main program body
 ******************************************************************************
 * @attention
 *
 * Copyright (c) 2016 STMicroelectronics.
 * All rights reserved.
 *
 * This software is licensed under terms that can be found in the LICENSE file
 * in the root directory of this software component.
 * If no LICENSE file comes with this software, it is provided AS-IS.
 *
 ******************************************************************************
 */

/* Includes ------------------------------------------------------------------*/

#include "main.h"
#include "cmsis_os.h"
#include "lv_demos.h"
#include "app_monitor.h"

/** @addtogroup Template_Project
 * @{
 */

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
static __IO uint32_t uwLsiFreq = 32000;  // 假设 LSI 频率为 32 kHz
static __IO uint32_t uwTimingDelay;
RCC_ClocksTypeDef RCC_Clocks;

/* Private function prototypes -----------------------------------------------*/

char msg[256] = {0};

/* Private functions ---------------------------------------------------------*/
uint8_t led_flag = 0;
void led_toggle(void) {
  led_flag += 1;
  GPIO_WriteBit(GPIOC, GPIO_Pin_13, led_flag % 2);  //
}

void preInitialization(void) {
#ifdef USE_USART_DEBUG
  /* Setup USART for debug */
  usartSetup();
#endif /* USE_USART_DEBUG */
}

void usartSetup(void) {
  /* Enable APB1/AHB1 peripheral clock for USART2 & GPIOA */
  /* By default the USART2 communication is enabled */
  RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART2, ENABLE);
  // RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);

  /* Initializes the GPIOA peripheral as USART2 */
  GPIO_InitTypeDef GPIOA_Init;
  memset(&GPIOA_Init, 0, sizeof(GPIOA_Init));

  GPIOA_Init.GPIO_Pin = GPIO_Pin_2 | GPIO_Pin_3;
  GPIOA_Init.GPIO_Mode = GPIO_Mode_AF;
  GPIOA_Init.GPIO_PuPd = GPIO_PuPd_UP;

  GPIO_Init(GPIOA, &GPIOA_Init);

  /* AF configure mode 7 */
  GPIO_PinAFConfig(GPIOA, GPIO_PinSource2, GPIO_AF_USART2);
  GPIO_PinAFConfig(GPIOA, GPIO_PinSource3, GPIO_AF_USART2);

  /* Initializes the USART2 peripheral */
  USART_InitTypeDef USART2_Init;
  memset(&USART2_Init, 0, sizeof(USART2_Init));

  USART2_Init.USART_BaudRate = 115200;
  USART2_Init.USART_WordLength = USART_WordLength_8b;
  USART2_Init.USART_StopBits = USART_StopBits_1;
  USART2_Init.USART_Parity = USART_Parity_No;
  USART2_Init.USART_Mode = USART_Mode_Tx | USART_Mode_Rx;
  USART2_Init.USART_HardwareFlowControl = USART_HardwareFlowControl_None;

  USART_Init(USART2, &USART2_Init);

  /* Enable USART2 peripheral */
  USART_Cmd(USART2, ENABLE);
}

void sendData(char *msg) {
  for (uint16_t i = 0; i < strlen(msg); i++) {
    while (USART_GetFlagStatus(USART2, USART_FLAG_TXE) != SET)
      ;
    USART_SendData(USART2, msg[i]);
  }
}

static void io_init(void) {
  GPIO_InitTypeDef GPIO_InitStructure;
  // /* Configure MCO1 pin(PA8) in alternate function */
  // GPIO_InitStructure.GPIO_Pin = GPIO_Pin_8;
  // GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
  // GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
  // GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
  // GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
  // GPIO_Init(GPIOA, &GPIO_InitStructure);

  /* HSE clock selected to output on MCO1 pin(PA8)*/
  RCC_MCO1Config(RCC_MCO1Source_HSE, RCC_MCO1Div_1);

  /* Output SYSCLK/4 clock on MCO2 pin(PC9) ***********************************/
  /* Enable the GPIOACperipheral */

  // /* Configure MCO2 pin(PC9) in alternate function */
  // GPIO_InitStructure.GPIO_Pin = GPIO_Pin_9;
  // GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
  // GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;
  // GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
  // GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
  // GPIO_Init(GPIOC, &GPIO_InitStructure);

  /* Configure MCO2 pin(PC13) in alternate function */
  GPIO_InitStructure.GPIO_Pin = GPIO_Pin_13;
  GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;
  GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;
  GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;
  // GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;
  GPIO_Init(GPIOC, &GPIO_InitStructure);

  // /* SYSCLK/4 clock selected to output on MCO2 pin(PC9)*/
  // RCC_MCO2Config(RCC_MCO2Source_SYSCLK, RCC_MCO2Div_4);
}

static void system_lcd_task(void *para) {
  while (1) {
    lv_timer_handler();
    vTaskDelay(pdMS_TO_TICKS(5));
  }
  vTaskDelete(NULL);
}

void FPU_Enable(void) {
  /* 设置 CP10 和 CP11 的访问权限为全访问（0b11） */
  SCB->CPACR |= ((3UL << 20) | (3UL << 22));

  /* 同步指令和数据缓存，确保设置立即生效 */
  __DSB();
  __ISB();
}

/* Definitions for defaultTask */
osThreadId_t defaultTaskHandle;
const osThreadAttr_t defaultTask_attributes = {
  .name = "defaultTask",
  .stack_size = 128 * 4,
  .priority = (osPriority_t)osPriorityRealtime,
};

/**
 * @brief  Function implementing the defaultTask thread.
 * @param  argument: Not used
 * @retval None
 */
/* USER CODE END Header_StartDefaultTask */
static uint16_t main_sys_tick_num = 0;
void StartDefaultTask(void *argument) {
  INA226_Data_t ina226_data;

  while (1) {
    if (main_sys_tick_num % 10 == 1) {
      led_toggle();
    }

    if (main_sys_tick_num % 300 == 1) {
      // 查询当前可用堆内存（单位：字节）
      size_t freeHeap = xPortGetFreeHeapSize();
      // 查询运行过程中最低剩余堆内存
      size_t minEverFreeHeap = xPortGetMinimumEverFreeHeapSize();

      // 输出内存信息（这里假设你有一个 printf 或串口打印函数）
      logi(
        "Current Free Heap: %u bytes, Minimum Ever Free Heap: %u bytes\r\n",
        (unsigned int)freeHeap,
        (unsigned int)minEverFreeHeap);
    }

    // Check for new INA226 data from the queue
    // Drain all data from queue to get the latest measurement
    bool data_updated = false;
    // while (App_Monitor_Get_Data(&ina226_data, 0)) { // Non-blocking read
    //     data_updated = true;
    // }

    // // Update LCD display only if we got new data
    // if (data_updated) {
    //     ui_update_ina226_data(ina226_data.voltage_V,
    //                           ina226_data.current_A,
    //                           ina226_data.power_W,
    //                           ina226_data.shunt_voltage_mV);
    // }

    main_sys_tick_num++;
    vTaskDelay(pdMS_TO_TICKS(10));
  }
  vTaskDelete(NULL);
  /* USER CODE END 5 */
}

/**
 * @brief  Main program
 * @param  None
 * @retval None
 */
int main(void) {
  NVIC_PriorityGroupConfig(NVIC_PriorityGroup_4);  // 设置系统中断优先级分组4 (required for FreeRTOS)

  /*!< At this stage the microcontroller clock setting is already configured,
        this is done through SystemInit() function which is called from startup
        files before to branch to application main.
        To reconfigure the default setting of SystemInit() function,
        refer to system_stm32f4xx.c file */

  /* SysTick end of count event each 10ms */
  RCC_GetClocksFreq(&RCC_Clocks);
  SysTick_Config(RCC_Clocks.HCLK_Frequency / 100);

  /* Add your application code here */
  /* Insert 50 ms delay */
  s_delay_ms(10);

  FPU_Enable();

  // RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA | RCC_AHB1Periph_GPIOB | RCC_AHB1Periph_GPIOC |
  //                       RCC_AHB1Periph_GPIOD | RCC_AHB1Periph_GPIOE | RCC_AHB1Periph_GPIOF |
  //                       RCC_AHB1Periph_GPIOG | RCC_AHB1Periph_GPIOH | RCC_AHB1Periph_GPIOI, ENABLE);

  /* Output HSE clock on MCO1 pin(PA8) ****************************************/
  /* Enable the GPIOA peripheral */
  RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);
  RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOB, ENABLE);
  RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOC, ENABLE);

  usartSetup();

  io_init();

  logi("io_init \n");

  /* Check if the system has resumed from IWDG reset */
  if (RCC_GetFlagStatus(RCC_FLAG_IWDGRST) != RESET) {
    /* IWDGRST flag set */
    /* Turn on LED1 */
    logi("IWDGRST \n");

    /* Clear reset flags */
    RCC_ClearFlag();
  }

  s_delay_ms(100);
  led_toggle();
  s_delay_ms(100);

  __enable_irq();

  // lcd spi
  APP_SPI_Init();
  SPI1_DMA_Config();
  SPI1_DMA_NVIC_Config();
  SPI_Send_Queue_init();

  /* 启动 DMA 传输 */
  DMA_Cmd(DMA2_Stream3, ENABLE);  // 启动 SPI1 TX DMA
  DMA_Cmd(DMA2_Stream0, ENABLE);  // 启动 SPI1 RX DMA

  logi("APP_SPI_Init \n");

  s_delay_ms(100);

  ST7789V_LcdInit();

  app_disp_init();

  lcdClear(BLACK);

  // LVGL
  lv_init();
  lv_port_disp_init();
  lv_port_indev_init();

  s_delay_ms(10);

  logi("app start \n");

  // lv_demo_widgets();
  // ui_test();

  // Initialize UI demo module
  ui_demo_init();

  // Create INA226 display interface
  ui_create_ina226_display();

  // Initialize the INA226 monitor (timer, ISR, queue)
  App_Monitor_Init();

  /* Create the thread(s) */
  /* creation of defaultTask */
  // defaultTaskHandle = osThreadNew(StartDefaultTask, NULL, &defaultTask_attributes);

  uint8_t err = xTaskCreate(StartDefaultTask, "default_task", 256 * 8, NULL, 9, NULL);
  err = xTaskCreate(system_lcd_task, "lcd_task", 256 * 8, NULL, 0, NULL);

  /* Start scheduler */
  vTaskStartScheduler();

  for (;;)
    ;
}

#if 0
int main_bak(void)
{

  /* Resets the RCC clock configuration to the default reset state */
  /* HSI On, HSE & PLL Off */
  RCC_DeInit();

  /* Update SystemCoreClock variable according to Clock Register Values */
  /* System clock/CPU clock = 16MHz */
  SystemCoreClockUpdate();
	
  /* Initialization area */
  preInitialization();

  sprintf(msg, "App is running\r\n");
  sendData(msg);

  /* Create two tasks */
  // xTaskCreate(vTask1_Handler, "Task-1", configMINIMAL_STACK_SIZE, NULL, 2, &xTaskHandler1);
  // xTaskCreate(vTask2_Handler, "Task-2", configMINIMAL_STACK_SIZE, NULL, 2, &xTaskHandler2);
  uint8_t err = xTaskCreate(system_lcd_task, "lcd_task", configMINIMAL_STACK_SIZE , NULL,  3, NULL); 
  
  /* Start scheduler */
  vTaskStartScheduler();

  for(;;);
}
#endif

#ifdef USE_FULL_ASSERT

/**
 * @brief  Reports the name of the source file and the source line number
 *         where the assert_param error has occurred.
 * @param  file: pointer to the source file name
 * @param  line: assert_param error line source number
 * @retval None
 */
void assert_failed(uint8_t *file, uint32_t line) {
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */

  /* Infinite loop */
  while (1) {
  }
}
#endif

/**
 * @}
 */
